#!/bin/bash

# NVM 安装脚本 - Ubuntu 20.04
# 使用方法: chmod +x install-nvm.sh && ./install-nvm.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统
check_system() {
    log_info "检查系统环境..."
    
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_warning "此脚本专为 Ubuntu 设计，其他系统可能需要调整"
    fi
    
    # 检查网络连接
    if ! ping -c 1 github.com &> /dev/null; then
        log_error "无法连接到 GitHub，请检查网络连接"
        exit 1
    fi
    
    log_success "系统检查通过"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包列表
    sudo apt update
    
    # 安装必要的依赖
    sudo apt install -y curl wget git build-essential
    
    log_success "系统依赖安装完成"
}

# 安装 nvm
install_nvm() {
    log_info "安装 nvm..."
    
    # 获取最新版本号
    NVM_VERSION=$(curl -s https://api.github.com/repos/nvm-sh/nvm/releases/latest | grep '"tag_name"' | cut -d'"' -f4)
    
    if [ -z "$NVM_VERSION" ]; then
        log_warning "无法获取最新版本，使用默认版本 v0.39.0"
        NVM_VERSION="v0.39.0"
    fi
    
    log_info "下载 nvm $NVM_VERSION..."
    
    # 下载并安装 nvm
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/$NVM_VERSION/install.sh | bash
    
    log_success "nvm 下载完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 添加 nvm 到 shell 配置文件
    NVM_DIR="$HOME/.nvm"
    
    # 检查并添加到 .bashrc
    if ! grep -q "NVM_DIR" ~/.bashrc; then
        echo '' >> ~/.bashrc
        echo '# NVM Configuration' >> ~/.bashrc
        echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.bashrc
        echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm' >> ~/.bashrc
        echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion' >> ~/.bashrc
        log_info "已添加 nvm 配置到 ~/.bashrc"
    fi
    
    # 检查并添加到 .zshrc (如果存在)
    if [ -f ~/.zshrc ] && ! grep -q "NVM_DIR" ~/.zshrc; then
        echo '' >> ~/.zshrc
        echo '# NVM Configuration' >> ~/.zshrc
        echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.zshrc
        echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm' >> ~/.zshrc
        echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion' >> ~/.zshrc
        log_info "已添加 nvm 配置到 ~/.zshrc"
    fi
    
    # 立即加载 nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
    
    log_success "环境变量配置完成"
}

# 安装 Node.js
install_nodejs() {
    log_info "安装 Node.js..."
    
    # 重新加载 nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    
    # 安装最新的 LTS 版本
    log_info "安装 Node.js LTS 版本..."
    nvm install --lts
    
    # 设置为默认版本
    nvm use --lts
    nvm alias default lts/*
    
    # 验证安装
    NODE_VERSION=$(node -v)
    NPM_VERSION=$(npm -v)
    
    log_success "Node.js 安装完成: $NODE_VERSION"
    log_success "npm 版本: $NPM_VERSION"
}

# 安装 pnpm
install_pnpm() {
    log_info "安装 pnpm..."
    
    # 使用 npm 安装 pnpm
    npm install -g pnpm@latest
    
    PNPM_VERSION=$(pnpm -v)
    log_success "pnpm 安装完成: $PNPM_VERSION"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 重新加载环境
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    
    echo ""
    echo "=== 安装信息 ==="
    echo "nvm 版本: $(nvm --version)"
    echo "Node.js 版本: $(node -v)"
    echo "npm 版本: $(npm -v)"
    echo "pnpm 版本: $(pnpm -v)"
    
    echo ""
    echo "=== 可用的 Node.js 版本 ==="
    nvm list
    
    log_success "安装验证完成"
}

# 显示使用说明
show_usage() {
    echo ""
    echo "🎉 nvm 安装完成！"
    echo "===================="
    echo ""
    echo "📋 常用命令:"
    echo "  nvm list                    # 查看已安装的 Node.js 版本"
    echo "  nvm list-remote             # 查看可安装的 Node.js 版本"
    echo "  nvm install 18.19.0        # 安装指定版本的 Node.js"
    echo "  nvm install --lts          # 安装最新的 LTS 版本"
    echo "  nvm use 18.19.0            # 切换到指定版本"
    echo "  nvm use --lts              # 切换到 LTS 版本"
    echo "  nvm alias default 18.19.0  # 设置默认版本"
    echo "  nvm current                # 查看当前使用的版本"
    echo ""
    echo "🔄 重新加载环境:"
    echo "  source ~/.bashrc           # 重新加载 bash 配置"
    echo "  # 或者重新打开终端"
    echo ""
    echo "🚀 DeepSearch 项目启动:"
    echo "  cd /path/to/deepsearch"
    echo "  nvm use --lts              # 确保使用正确的 Node.js 版本"
    echo "  pnpm install               # 安装依赖"
    echo "  pnpm install:base          # 安装基础组件"
    echo "  pnpm dev                   # 启动开发服务器"
    echo ""
    echo "💡 提示: 如果命令不生效，请重新打开终端或运行 'source ~/.bashrc'"
}

# 主函数
main() {
    echo "🚀 开始安装 nvm 和 Node.js..."
    echo "================================"
    
    check_system
    install_dependencies
    install_nvm
    setup_environment
    install_nodejs
    install_pnpm
    verify_installation
    show_usage
    
    echo ""
    log_success "🎉 所有组件安装完成！"
    echo ""
    log_warning "⚠️  请重新打开终端或运行以下命令来加载 nvm:"
    echo "   source ~/.bashrc"
}

# 执行主函数
main "$@"
